import React from "react"
import { Typography } from "@apollo/ui"
import { Gift, ShoppingCart, Star } from "@design-systems/apollo-icons"
import ResourceCard from "../../components/resource-card/ResourceCard"
import { Meta } from "@storybook/addon-docs/blocks"

<Meta title="@apollo∕storefront/Catalog" tags={["docs"]} />

# Components

<Typography level="headlineMedium" style={{ margin: "0 0 16px 0", color: "#121212" }}>Product Components</Typography>
<Typography
  level="bodyMedium"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 16,
  }}
>
  Essential components for displaying products, pricing, and product information in your storefront.
</Typography>

<div
  style={{
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(260px, 1fr))",
    gap: 16,
    marginBottom: 32,
  }}
>
  <ResourceCard
    page="apollo∕storefront-components-data-display-productcard"
    title="ProductCard"
    description="Complete product display with image, title, and pricing"
  />
  <ResourceCard
    page="apollo∕storefront-components-data-display-productcardcontent"
    title="ProductCardContent"
    description="Flexible content area for product information"
  />
  <ResourceCard
    page="apollo∕storefront-components-data-display-productprice"
    title="ProductPrice"
    description="Pricing display with discount and currency support"
  />
  <ResourceCard
    page="apollo∕storefront-components-data-display-productbadge"
    title="ProductBadge"
    description="Status badges for products (sale, new, featured)"
  />
</div>

<Typography level="headlineMedium" style={{ margin: "0 0 16px 0", color: "#121212" }}>Navigation Components</Typography>
<Typography
  level="bodyMedium"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 16,
  }}
>
  Enhanced navigation components designed for visual browsing and category selection.
</Typography>

<div
  style={{
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(260px, 1fr))",
    gap: 16,
    marginBottom: 32,
  }}
>
  <ResourceCard
    page="apollo∕storefront-components-navigation-tabwithimage"
    title="TabWithImage"
    description="Tabbed navigation with image support for categories"
  />
</div>