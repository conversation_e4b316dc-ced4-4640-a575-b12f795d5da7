import React, { useState, useMemo } from "react"
import { Typography, Input } from "@apollo/ui"
import { Gift, ShoppingCart, Star, Search } from "@design-systems/apollo-icons"
import ResourceCard from "../../components/resource-card/ResourceCard"
import { Meta } from "@storybook/addon-docs/blocks"

<Meta title="@apollo∕storefront/Catalog" tags={["docs"]} />

# Catalog

export const ComponentCatalog = () => {
  const [searchTerm, setSearchTerm] = useState("")

  const components = [
    {
      category: "Product Components",
      description: "Essential components for displaying products, pricing, and product information in your storefront.",
      items: [
        {
          page: "apollo∕storefront-components-data-display-productcard",
          title: "ProductCard",
          description: "Complete product display with image, title, and pricing",
          keywords: ["product", "card", "display", "image", "title", "pricing"]
        },
        {
          page: "apollo∕storefront-components-data-display-productcardcontent",
          title: "ProductCardContent",
          description: "Flexible content area for product information",
          keywords: ["product", "card", "content", "information", "flexible"]
        },
        {
          page: "apollo∕storefront-components-data-display-productprice",
          title: "ProductPrice",
          description: "Pricing display with discount and currency support",
          keywords: ["price", "pricing", "discount", "currency", "money"]
        },
        {
          page: "apollo∕storefront-components-data-display-productbadge",
          title: "ProductBadge",
          description: "Status badges for products (sale, new, featured)",
          keywords: ["badge", "status", "sale", "new", "featured", "label"]
        }
      ]
    },
    {
      category: "Navigation Components",
      description: "Enhanced navigation components designed for visual browsing and category selection.",
      items: [
        {
          page: "apollo∕storefront-components-navigation-tabwithimage",
          title: "TabWithImage",
          description: "Tabbed navigation with image support for categories",
          keywords: ["tab", "navigation", "image", "category", "visual", "browsing"]
        }
      ]
    }
  ]

  const filteredComponents = useMemo(() => {
    if (!searchTerm) return components

    return components.map(category => ({
      ...category,
      items: category.items.filter(item => {
        const searchLower = searchTerm.toLowerCase()
        return (
          item.title.toLowerCase().includes(searchLower) ||
          item.description.toLowerCase().includes(searchLower) ||
          item.keywords.some(keyword => keyword.toLowerCase().includes(searchLower))
        )
      })
    })).filter(category => category.items.length > 0)
  }, [searchTerm, components])

  return (
    <div>
      {/* Search Input */}
      <div style={{ marginBottom: "32px" }}>
        <Input
          placeholder="Search components..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          startDecorator={<Search size={20} />}
          fullWidth
        />
      </div>

      {/* Results */}
      {filteredComponents.length === 0 ? (
        <div style={{
          textAlign: "center",
          padding: "48px 24px",
          color: "var(--sb-secondary-text-color)"
        }}>
          <Typography level="bodyLarge">
            No components found for "{searchTerm}"
          </Typography>
          <Typography level="bodyMedium" style={{ marginTop: "8px" }}>
            Try searching for "product", "card", "price", "badge", or "tab"
          </Typography>
        </div>
      ) : (
        filteredComponents.map((category) => (
          <div key={category.category} style={{ marginBottom: "48px" }}>
            <Typography level="headlineMedium" style={{ margin: "0 0 16px 0", color: "#121212" }}>
              {category.category}
            </Typography>
            <Typography
              level="bodyMedium"
              style={{
                color: "var(--sb-secondary-text-color)",
                marginBottom: 16,
              }}
            >
              {category.description}
            </Typography>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(260px, 1fr))",
                gap: 16,
                marginBottom: 32,
              }}
            >
              {category.items.map((item) => (
                <ResourceCard
                  key={item.page}
                  page={item.page}
                  title={item.title}
                  description={item.description}
                />
              ))}
            </div>
          </div>
        ))
      )}
    </div>
  )
}

<ComponentCatalog />
