import React, { useState } from "react"
import { Button, Input, Typography, createThemeV2, ApolloProvider } from "@apollo/ui"
import { Meta } from "@storybook/addon-docs/blocks"

import CautionCard from "../../components/caution-card/CautionCard"

<Meta title="@apollo∕storefront/Quick Start" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="displayMedium"
    style={{ margin: "0px", color: "#121212" }}
  >Quick Start</Typography>
  <Typography
    align="center"
    level="titleLarge"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Get started with Apollo Storefront in minutes!</Typography>
</div>


Currently our Apollo Storefront is a specialized component library for e-commerce experiences, built on top of the Apollo Design System.

To install the `@apollo/storefront` package, we need to configure the `.npmrc` file (create this file in the project root) to point to our custom private registry on GitLab.

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
```

## Install

<CautionCard>
If you're outside CJ network, VPN is needed to install this package.
</CautionCard>


```bash
# with pnpm (recommended)
pnpm add @apollo/storefront

# with yarn
yarn add @apollo/storefront

# with npm
npm install @apollo/storefront
```

## Setup

Apollo Storefront components are styled using CSS variables. To ensure consistent styling, you need to use the `<ApolloProvider />` from the `@apollo/ui` package.

Place a `<ApolloProvider />` at the root of your app and pass theme as a prop.

```jsx title="App.js"
import React from 'react';
import { ApolloProvider, createThemeV2 } from "@apollo/ui"

// Import styles
import "@apollo/ui/style.css"
import "@apollo/storefront/style.css"

const appTheme = createThemeV2()

function App({ children }) {
  return <ApolloProvider themeProps={{ theme: appTheme }}>{children}</ApolloProvider>
}
```

## Usage

You can import and use components directly:

```jsx title="Product Example"
import React from 'react'
import { ProductCard, ProductCardContent, ProductPrice } from "@apollo/storefront"

export default function Example() {
  return (
    <ProductCard>
      <img src="/product.jpg" alt="Product" />
      <ProductCardContent>
        <h3>Product Name</h3>
        <ProductPrice price={99} currency="$" />
      </ProductCardContent>
    </ProductCard>
  )
}
```

<div style={{
  background: "linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)",
  borderRadius: "12px",
  padding: "20px",
  margin: "16px 0",
  border: "1px solid #f59e0b",
  borderLeft: "4px solid #f59e0b"
}}>
  <h4 style={{
    margin: "0 0 12px 0",
    color: "#92400e",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>⚠️</span> Private Registry Required
  </h4>
  <p style={{ margin: "0", color: "#92400e" }}>
    Apollo Storefront is part of our private design system. You'll need access to the CJ Express GitLab registry and VPN connection if outside the network.
  </p>
</div>

## Installation

### Step 1: Configure Registry

Create or update your `.npmrc` file in your project root:

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
//gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/:_authToken=<AUTH_TOKEN>
```

<CautionCard>
If you're outside CJ network, VPN is needed to install this package.
</CautionCard>


### Step 2: Install Packages

Install both Apollo UI (if not already installed) and Apollo Storefront:

```bash
# Install Apollo UI (required peer dependency)
pnpm add @apollo/ui

# Install Apollo Storefront
pnpm add @apollo/storefront

# Or install both at once
pnpm add @apollo/ui @apollo/storefront
```

<details>
<summary>Other package managers</summary>

```bash
# with yarn
yarn add @apollo/ui @apollo/storefront

# with npm
npm install @apollo/ui @apollo/storefront
```
</details>

## Setup

### Step 1: Theme Configuration

Set up the Apollo provider with theme configuration in your app root:

```jsx title="App.js"
import React from 'react'
import { ApolloProvider, createThemeV2 } from "@apollo/ui"

// Import styles
import "@apollo/ui/style.css"
import "@apollo/storefront/style.css"

const appTheme = createThemeV2()

function App({ children }) {
  return (
    <ApolloProvider themeProps={{ theme: appTheme }}>
      {children}
    </ApolloProvider>
  )
}

export default App
```

### Step 2: Verify Installation

Create a simple test component to verify everything is working:

```jsx title="TestStorefront.jsx"
import React from 'react'
import {
  ProductCard,
  ProductCardContent,
  ProductPrice
} from "@apollo/storefront"

export default function TestStorefront() {
  return (
    <ProductCard style={{ maxWidth: 300 }}>
      <img
        src="https://via.placeholder.com/300x200"
        alt="Test Product"
        style={{ width: "100%", borderRadius: 8 }}
      />
      <ProductCardContent>
        <h3>Test Product</h3>
        <p>This is a test product to verify Apollo Storefront is working.</p>
        <ProductPrice price={99} currency="$" />
      </ProductCardContent>
    </ProductCard>
  )
}
```

## Basic Usage

### Product Display

Here's a complete example of creating a product display:

```jsx title="ProductDisplay.jsx"
import React from 'react'
import {
  ProductCard,
  ProductCardContent,
  ProductPrice,
  ProductBadge
} from "@apollo/storefront"

export default function ProductDisplay() {
  return (
    <div style={{
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))",
      gap: 16
    }}>
      {/* Featured Product */}
      <ProductCard>
        <ProductBadge variant="featured">Featured</ProductBadge>
        <img
          src="/product-1.jpg"
          alt="Premium Headphones"
          style={{ width: "100%", borderRadius: 8 }}
        />
        <ProductCardContent>
          <h3>Premium Headphones</h3>
          <p>High-quality wireless headphones with noise cancellation</p>
          <ProductPrice
            price={299}
            originalPrice={399}
            currency="$"
          />
        </ProductCardContent>
      </ProductCard>

      {/* Sale Product */}
      <ProductCard>
        <ProductBadge variant="sale">Sale</ProductBadge>
        <img
          src="/product-2.jpg"
          alt="Smart Watch"
          style={{ width: "100%", borderRadius: 8 }}
        />
        <ProductCardContent>
          <h3>Smart Watch</h3>
          <p>Track your fitness and stay connected</p>
          <ProductPrice
            price={199}
            originalPrice={299}
            currency="$"
          />
        </ProductCardContent>
      </ProductCard>
    </div>
  )
}
```

### Navigation Components

Apollo Storefront also includes enhanced navigation components:

```jsx title="CategoryNavigation.jsx"
import React from 'react'
import { TabWithImage } from "@apollo/storefront"

export default function CategoryNavigation() {
  return (
    <TabWithImage.Root defaultValue="electronics">
      <TabWithImage.List>
        <TabWithImage.Tab value="electronics">
          <TabWithImage.Image
            src="/category-electronics.jpg"
            alt="Electronics"
          />
          <TabWithImage.Text>Electronics</TabWithImage.Text>
        </TabWithImage.Tab>

        <TabWithImage.Tab value="clothing">
          <TabWithImage.Image
            src="/category-clothing.jpg"
            alt="Clothing"
          />
          <TabWithImage.Text>Clothing</TabWithImage.Text>
        </TabWithImage.Tab>

        <TabWithImage.Tab value="home">
          <TabWithImage.Image
            src="/category-home.jpg"
            alt="Home & Garden"
          />
          <TabWithImage.Text>Home & Garden</TabWithImage.Text>
        </TabWithImage.Tab>
      </TabWithImage.List>

      <TabWithImage.Panel value="electronics">
        Electronics products content...
      </TabWithImage.Panel>

      <TabWithImage.Panel value="clothing">
        Clothing products content...
      </TabWithImage.Panel>

      <TabWithImage.Panel value="home">
        Home & Garden products content...
      </TabWithImage.Panel>
    </TabWithImage.Root>
  )
}
```

## Next Steps

Now that you have Apollo Storefront set up, explore the available components:

- **[Component Catalog](/story/apollo-storefront-catalog--docs)** - Browse all available components
- **[ProductCard](/story/apollo-storefront-components-data-display-productcard--overview)** - Learn about product display options
- **[ProductPrice](/story/apollo-storefront-components-data-display-productprice--overview)** - Understand pricing display features
- **[TabWithImage](/story/apollo-storefront-components-navigation-tabwithimage--overview)** - Explore visual navigation options

## Troubleshooting

### Common Issues

**Components not styled correctly:**
- Make sure you've imported both `@apollo/ui/style.css` and `@apollo/storefront/style.css`
- Verify that `ApolloProvider` is wrapping your app

**Import errors:**
- Check that both `@apollo/ui` and `@apollo/storefront` are installed
- Verify your `.npmrc` configuration for private registry access

**TypeScript errors:**
- Ensure you have the latest type definitions
- Check that your TypeScript configuration includes the necessary DOM types

### Getting Help

- Check the [component documentation](/story/apollo-storefront-catalog--docs) for detailed usage examples
- Review the [changelog](/story/apollo-storefront-changelog--docs) for recent updates
- Contact the design system team for additional support