import React from "react"
import { Typography } from "@apollo/ui"
import { Shop, Gift, ShoppingCart, Star, Mobile } from "@design-systems/apollo-icons"
import ResourceCard from "../../components/resource-card/ResourceCard"
import { Meta } from "@storybook/addon-docs/blocks"

<Meta title="@apollo∕storefront/Introduction" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="displayMedium"
    style={{ margin: "0px", color: "#121212" }}
  >@apollo/storefront</Typography>
  <Typography
    align="center"
    level="titleLarge"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Specialized e-commerce components for mobile-first storefront experiences</Typography>
  <div
    style={{
      display: "flex",
      gap: 8,
      flexWrap: "wrap",
      justifyContent: "center",
      padding: "32px",
      minWidth: "460px",
    }}
  >
    <Shop size={64} style={{ color: "var(--sb-link-color)" }} />
  </div>
</div>

## What is Apollo Storefront?

<Typography
  level="bodyLarge"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 32,
  }}
>
  Apollo Storefront is a specialized component library designed for building modern e-commerce experiences, particularly optimized for mobile development. Built on top of <code style={{ background: "rgb(247, 250, 252)", color: "rgba(46, 52, 56, 0.9)", padding: "3px 5px", borderRadius: 3 }}>@apollo/ui</code>, it provides a curated set of components specifically tailored for storefront interfaces, product displays, and shopping experiences.
</Typography>

## Key Features

<div style={{ marginBottom: 32 }}>
  <ul style={{
    listStyle: "none",
    padding: 0,
    display: "grid",
    gap: 16,
    gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))"
  }}>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      background: "var(--sb-allgrey-background-color)"
    }}>
      <Mobile size={24} style={{ color: "var(--sb-link-color)", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Mobile-First Design</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Optimized for mobile shopping experiences with touch-friendly interactions
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      background: "var(--sb-allgrey-background-color)"
    }}>
      <Gift size={24} style={{ color: "var(--sb-link-color)", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>E-commerce Focused</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Purpose-built components for product displays, pricing, and shopping workflows
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      background: "var(--sb-allgrey-background-color)"
    }}>
      <Star size={24} style={{ color: "var(--sb-link-color)", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Design System Integration</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Seamlessly integrates with Apollo design tokens and theming system
        </span>
      </div>
    </li>
  </ul>
</div>

## Components

<Typography level="headlineMedium" style={{ margin: "0 0 16px 0", color: "#121212" }}>Product Components</Typography>
<Typography
  level="bodyMedium"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 16,
  }}
>
  Essential components for displaying products, pricing, and product information in your storefront.
</Typography>

<div
  style={{
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(260px, 1fr))",
    gap: 16,
    marginBottom: 32,
  }}
>
  <ResourceCard
    page="apollo∕storefront-components-data-display-productcard"
    title="ProductCard"
    description="Complete product display with image, title, and pricing"
    icon={<Gift size={24} />}
  />
  <ResourceCard
    page="apollo∕storefront-components-data-display-productcardcontent"
    title="ProductCardContent"
    description="Flexible content area for product information"
    icon={<Gift size={24} />}
  />
  <ResourceCard
    page="apollo∕storefront-components-data-display-productprice"
    title="ProductPrice"
    description="Pricing display with discount and currency support"
    icon={<Gift size={24} />}
  />
  <ResourceCard
    page="apollo∕storefront-components-data-display-productbadge"
    title="ProductBadge"
    description="Status badges for products (sale, new, featured)"
    icon={<Star size={24} />}
  />
</div>

<Typography level="headlineMedium" style={{ margin: "0 0 16px 0", color: "#121212" }}>Navigation Components</Typography>
<Typography
  level="bodyMedium"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 16,
  }}
>
  Enhanced navigation components designed for visual browsing and category selection.
</Typography>

<div
  style={{
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(260px, 1fr))",
    gap: 16,
    marginBottom: 32,
  }}
>
  <ResourceCard
    page="apollo∕storefront-components-navigation-tabwithimage"
    title="TabWithImage"
    description="Tabbed navigation with image support for categories"
    icon={<ShoppingCart size={24} />}
  />
</div>

## Installation

<Typography level="headlineMedium" style={{ margin: "0 0 16px 0", color: "#121212" }}>Prerequisites</Typography>

<Typography
  level="bodyMedium"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 16,
  }}
>
  Apollo Storefront requires the Apollo Design System to be set up in your project. Make sure you have the following configured:
</Typography>

<div style={{
  background: "linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)",
  borderRadius: "12px",
  padding: "20px",
  margin: "16px 0",
  border: "1px solid #f59e0b",
  borderLeft: "4px solid #f59e0b"
}}>
  <h4 style={{
    margin: "0 0 12px 0",
    color: "#92400e",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>⚠️</span> Private Registry Required
  </h4>
  <p style={{ margin: "0", color: "#92400e" }}>
    Apollo Storefront is part of our private design system. You'll need access to the CJ Express GitLab registry and VPN connection if outside the network.
  </p>
</div>

Configure your `.npmrc` file:

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
//gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/:_authToken=<AUTH_TOKEN>
```

Install the package:

```bash
# with pnpm (recommended)
pnpm add @apollo/storefront

# with yarn
yarn add @apollo/storefront

# with npm
npm install @apollo/storefront
```

## Setup

<Typography
  level="bodyMedium"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 16,
  }}
>
  Apollo Storefront components work seamlessly with the Apollo theming system. Make sure you have the Apollo provider set up:
</Typography>

```jsx title="App.js"
import React from 'react';
import { ApolloProvider, createThemeV2 } from "@apollo/ui"

// Import storefront styles
import "@apollo/storefront/style.css"

const appTheme = createThemeV2()

function App({ children }) {
  return (
    <ApolloProvider themeProps={{ theme: appTheme }}>
      {children}
    </ApolloProvider>
  )
}
```

## Usage

<Typography
  level="bodyMedium"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 16,
  }}
>
  Import and use storefront components directly in your application:
</Typography>

```jsx title="Product Display Example"
import React from 'react'
import {
  ProductCard,
  ProductCardContent,
  ProductPrice,
  ProductBadge
} from "@apollo/storefront"

export default function ProductDisplay() {
  return (
    <ProductCard>
      <ProductBadge variant="sale">Sale</ProductBadge>
      <img
        src="/product-image.jpg"
        alt="Product"
        style={{ width: "100%", borderRadius: 8 }}
      />
      <ProductCardContent>
        <h3>Premium Headphones</h3>
        <p>High-quality wireless headphones with noise cancellation</p>
        <ProductPrice
          price={299}
          originalPrice={399}
          currency="USD"
        />
      </ProductCardContent>
    </ProductCard>
  )
}
```

## Design Tokens

<Typography
  level="bodyMedium"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 16,
  }}
>
  Apollo Storefront includes specialized design tokens optimized for e-commerce interfaces. These tokens extend the base Apollo token system with storefront-specific values:
</Typography>

```jsx title="Using Storefront Tokens"
import { ApolloStorefrontToken } from "@apollo/storefront"

// Access storefront-specific tokens
const storefrontTheme = {
  ...ApolloStorefrontToken,
  // Your custom overrides
}
```

## Best Practices

<div style={{ marginBottom: 32 }}>
  <ul style={{
    listStyle: "none",
    padding: 0,
    display: "flex",
    flexDirection: "column",
    gap: 16
  }}>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      background: "var(--sb-allgrey-background-color)"
    }}>
      <span style={{ color: "var(--sb-link-color)", fontSize: "18px", flexShrink: 0 }}>📱</span>
      <div>
        <strong>Mobile-First Approach</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Design for mobile screens first, then enhance for larger displays. Apollo Storefront components are optimized for touch interactions.
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      background: "var(--sb-allgrey-background-color)"
    }}>
      <span style={{ color: "var(--sb-link-color)", fontSize: "18px", flexShrink: 0 }}>🎨</span>
      <div>
        <strong>Consistent Theming</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Use Apollo design tokens for consistent spacing, colors, and typography across your storefront.
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      background: "var(--sb-allgrey-background-color)"
    }}>
      <span style={{ color: "var(--sb-link-color)", fontSize: "18px", flexShrink: 0 }}>⚡</span>
      <div>
        <strong>Performance Optimization</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Optimize images and use lazy loading for product displays. Consider virtualization for large product lists.
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      background: "var(--sb-allgrey-background-color)"
    }}>
      <span style={{ color: "var(--sb-link-color)", fontSize: "18px", flexShrink: 0 }}>♿</span>
      <div>
        <strong>Accessibility</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Ensure proper alt text for product images, keyboard navigation support, and screen reader compatibility.
        </span>
      </div>
    </li>
  </ul>
</div>

## Version Information

<Typography
  level="bodyMedium"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 16,
  }}
>
  Current version: <code style={{ background: "rgb(247, 250, 252)", color: "rgba(46, 52, 56, 0.9)", padding: "3px 5px", borderRadius: 3 }}>1.0.0-beta.1</code>
</Typography>

<div style={{
  background: "linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)",
  borderRadius: "12px",
  padding: "20px",
  margin: "16px 0",
  border: "1px solid #3b82f6",
  borderLeft: "4px solid #3b82f6"
}}>
  <h4 style={{
    margin: "0 0 12px 0",
    color: "#1e40af",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>🚀</span> Beta Release
  </h4>
  <p style={{ margin: "0", color: "#1e40af" }}>
    Apollo Storefront is currently in beta. APIs may change between releases. Please check the changelog for breaking changes when updating.
  </p>
</div>
