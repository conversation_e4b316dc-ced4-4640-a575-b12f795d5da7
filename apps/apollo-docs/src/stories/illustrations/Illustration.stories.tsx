import React from "react"
import { Typography } from "@apollo/ui"
import { Illustration } from "@design-systems/apollo-ui"
import {
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

import { IllustrationGalleryComponent } from "./components/IllustrationGalleryComponent"

/**
 *
 * The Apollo Illustrations package provides a comprehensive collection of SVG illustrations designed specifically
 * for the Apollo Design System. All illustrations are optimized for performance, accessibility, and
 * consistent visual design. These illustrations are commonly used with EmptyState components to enhance
 * user experience when there is no content to display.
 *
 */
const meta = {
  title: "Illustrations/Icons & Illustrations/Illustrations Library",
  component: Illustration.FileNotFound,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2199-18748&m=dev",
    },
    docs: {
      page: () => (
        <>
          <Title />
          <Subtitle>
            A comprehensive collection of illustrations for the Apollo Design
            System
          </Subtitle>
          <Description />
          <h2 id="illustration-installation">Installation</h2>
          <p>
            To use Apollo Illustrations in your project, you need to install the package
            and configure your registry:
          </p>

          <h3>1. Update .npmrc</h3>
          <Source
            code={`@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
//gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/:_authToken=<AUTH_TOKEN>`}
            language="text"
          />
          <h3>2. Install the Package</h3>
          <Source
            code={`# Using npm
npm install @design-systems/apollo-ui

# Using yarn  
yarn add @design-systems/apollo-ui

# Using pnpm
pnpm add @design-systems/apollo-ui`}
            language="bash"
          />

          <h2 id="illustration-usage">Usage</h2>
          <p>
            Import and use illustrations in your React components:
          </p>
          <Source
            code={`import { Illustration } from "@design-systems/apollo-ui"

function MyComponent() {
  return (
    <div>
      <Illustration.FileNotFound />
      <Illustration.AddInformation />
    </div>
  )
}`}
            language="tsx"
          />

          <h3>With EmptyState Component</h3>
          <p>
            Illustrations are commonly used with the EmptyState component:
          </p>
          <Source
            code={`import { EmptyState } from "@apollo/ui"

function MyComponent() {
  return (
    <EmptyState
      name="FileNotFound"
      title="No results found"
      description="Try adjusting your search criteria."
    />
  )
}`}
            language="tsx"
          />

          <h2 id="illustration-customization">Customization</h2>
          <p>
            Illustrations inherit the current text color and can be customized with standard SVG props:
          </p>
          <Source
            code={`// Custom size and color
<Illustration.FileNotFound 
  style={{ 
    width: 200, 
    height: 200, 
    color: '#3b82f6' 
  }} 
/>

// With CSS classes
<Illustration.AddInformation 
  className="w-32 h-32 text-blue-500" 
/>`}
            language="tsx"
          />

          <h2 id="illustration-accessibility">Accessibility</h2>
          <p>
            All illustrations include proper accessibility attributes. You can enhance accessibility by adding descriptive labels:
          </p>
          <Source
            code={`<Illustration.FileNotFound 
  aria-label="No files found illustration"
  role="img"
/>`}
            language="tsx"
          />

          <Primary />
          <Stories />
        </>
      ),
    },
  },
} satisfies Meta<typeof Illustration.FileNotFound>

export default meta
type Story = StoryObj<typeof meta>

/**
 * Overview examples showing different sizes and styling options.
 */
export const Overview: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "24px", flexWrap: "wrap" }}>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          alignItems: "center",
        }}
      >
        <Typography level="labelLarge">Default</Typography>
        <Illustration.FileNotFound style={{ width: 114, height: 114 }} />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          alignItems: "center",
        }}
      >
        <Typography level="labelLarge">Custom Color</Typography>
        <Illustration.FileNotFound
          style={{ 
            width: 114, 
            height: 114, 
            color: "var(--apl-alias-color-primary-primary)" 
          }}
        />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          alignItems: "center",
        }}
      >
        <Typography level="labelLarge">Large Size</Typography>
        <Illustration.AddInformation style={{ width: 150, height: 150 }} />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          alignItems: "center",
        }}
      >
        <Typography level="labelLarge">Small Size</Typography>
        <Illustration.AddInformation style={{ width: 80, height: 80 }} />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Illustrations inherit the current text color by default and can be styled with CSS color properties and size attributes.",
      },
      source: {
        code: `<Illustration.FileNotFound style={{ width: 114, height: 114 }} />
<Illustration.FileNotFound style={{ width: 114, height: 114, color: "var(--apl-alias-color-primary-primary)" }} />
<Illustration.AddInformation style={{ width: 150, height: 150 }} />
<Illustration.AddInformation style={{ width: 80, height: 80 }} />`,
      },
    },
  },
}

/**
 * Example showing illustrations used with EmptyState component for common scenarios.
 */
export const WithEmptyState: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "32px", flexWrap: "wrap" }}>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: "16px",
          padding: "24px",
          border: "1px solid #e5e7eb",
          borderRadius: "12px",
          maxWidth: "300px",
        }}
      >
        <Illustration.FileNotFound style={{ width: 114, height: 114 }} />
        <div style={{ textAlign: "center" }}>
          <Typography level="titleLarge" style={{ marginBottom: "8px" }}>
            No results found
          </Typography>
          <Typography level="bodyMedium" style={{ color: "#6b7280" }}>
            Try adjusting your search criteria to find what you're looking for.
          </Typography>
        </div>
      </div>

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: "16px",
          padding: "24px",
          border: "1px solid #e5e7eb",
          borderRadius: "12px",
          maxWidth: "300px",
        }}
      >
        <Illustration.AddInformation style={{ width: 114, height: 114 }} />
        <div style={{ textAlign: "center" }}>
          <Typography level="titleLarge" style={{ marginBottom: "8px" }}>
            Add your first item
          </Typography>
          <Typography level="bodyMedium" style={{ color: "#6b7280" }}>
            Get started by adding information to see your data here.
          </Typography>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Common usage patterns showing illustrations with EmptyState-style layouts for different scenarios.",
      },
      source: {
        code: `// File Not Found scenario
<div style={{ textAlign: "center" }}>
  <Illustration.FileNotFound style={{ width: 114, height: 114 }} />
  <Typography level="titleLarge">No results found</Typography>
  <Typography level="bodyMedium">Try adjusting your search criteria.</Typography>
</div>

// Add Information scenario
<div style={{ textAlign: "center" }}>
  <Illustration.AddInformation style={{ width: 114, height: 114 }} />
  <Typography level="titleLarge">Add your first item</Typography>
  <Typography level="bodyMedium">Get started by adding information.</Typography>
</div>`,
      },
    },
  },
}

/**
 * The main illustration gallery showcasing all available illustrations with search functionality.
 * Click on any illustration to copy its import statement to your clipboard.
 */
export const IllustrationLibrary: Story = {
  render: () => <IllustrationGalleryComponent />,
  parameters: {
    docs: {
      description: {
        story:
          "Interactive gallery of all available Apollo illustrations with search and copy functionality.",
      },
    },
  },
}
