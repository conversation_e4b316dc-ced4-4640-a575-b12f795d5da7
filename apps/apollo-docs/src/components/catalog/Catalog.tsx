import React, { useState, useMemo } from "react"
import { Typography, Input } from "@apollo/ui"
import { Search } from "@design-systems/apollo-icons"
import ResourceCard from "../resource-card/ResourceCard"

interface Item {
  page: string
  title: string
  description: string
  keywords: string[]
}

interface Category {
  category: string
  description: string
  items: Item[]
}

const Catalog: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("")

  const components: Category[] = [
    {
      category: "Product Components",
      description: "Essential components for displaying products, pricing, and product information in your storefront.",
      items: [
        {
          page: "apollo∕storefront-components-data-display-productcard",
          title: "ProductCard",
          description: "Complete product display with image, title, and pricing",
          keywords: ["product", "card", "display", "image", "title", "pricing"]
        },
        {
          page: "apollo∕storefront-components-data-display-productcardcontent",
          title: "ProductCardContent",
          description: "Flexible content area for product information",
          keywords: ["product", "card", "content", "information", "flexible"]
        },
        {
          page: "apollo∕storefront-components-data-display-productprice",
          title: "ProductPrice",
          description: "Pricing display with discount and currency support",
          keywords: ["price", "pricing", "discount", "currency", "money"]
        },
        {
          page: "apollo∕storefront-components-data-display-productbadge",
          title: "ProductBadge",
          description: "Status badges for products (sale, new, featured)",
          keywords: ["badge", "status", "sale", "new", "featured", "label"]
        }
      ]
    },
    {
      category: "Navigation Components",
      description: "Enhanced navigation components designed for visual browsing and category selection.",
      items: [
        {
          page: "apollo∕storefront-components-navigation-tabwithimage",
          title: "TabWithImage",
          description: "Tabbed navigation with image support for categories",
          keywords: ["tab", "navigation", "image", "category", "visual", "browsing"]
        }
      ]
    }
  ]

  const filteredComponents = useMemo(() => {
    if (!searchTerm) return components

    return components.map(category => ({
      ...category,
      items: category.items.filter(item => {
        const searchLower = searchTerm.toLowerCase()
        return (
          item.title.toLowerCase().includes(searchLower) ||
          item.description.toLowerCase().includes(searchLower) ||
          item.keywords.some(keyword => keyword.toLowerCase().includes(searchLower))
        )
      })
    })).filter(category => category.items.length > 0)
  }, [searchTerm, components])

  return (
    <div style={{ maxWidth: "1200px", margin: "0 auto" }}>
      {/* Search Section */}
      <div style={{ 
        marginBottom: "40px",
        padding: "24px",
        backgroundColor: "#f8f9fa",
        borderRadius: "12px",
        border: "1px solid #e9ecef"
      }}>
        <Typography 
          level="titleLarge" 
          style={{ 
            marginBottom: "16px", 
            color: "#121212",
            fontWeight: 600
          }}
        >
          Search Components
        </Typography>
        <Input
          placeholder="Search by component name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          startDecorator={<Search size={20} />}
          fullWidth
          style={{
            backgroundColor: "white",
            border: "1px solid #dee2e6"
          }}
        />
      </div>

      {/* Results Section */}
      {filteredComponents.length === 0 ? (
        <div style={{
          textAlign: "center",
          padding: "64px 24px",
          backgroundColor: "#f8f9fa",
          borderRadius: "12px",
          border: "1px solid #e9ecef"
        }}>
          <Typography level="headlineSmall" style={{ color: "#6c757d", marginBottom: "8px" }}>
            No components found
          </Typography>
          <Typography level="bodyLarge" style={{ color: "#6c757d", marginBottom: "16px" }}>
            No components match "{searchTerm}"
          </Typography>
          <Typography level="bodyMedium" style={{ color: "#adb5bd" }}>
            Try searching for "product", "card", "price", "badge", or "tab"
          </Typography>
        </div>
      ) : (
        <div style={{ display: "flex", flexDirection: "column", gap: "48px" }}>
          {filteredComponents.map((category) => (
            <div key={category.category}>
              <div style={{ marginBottom: "24px" }}>
                <Typography 
                  level="headlineMedium" 
                  style={{ 
                    margin: "0 0 8px 0", 
                    color: "#121212",
                    fontWeight: 600
                  }}
                >
                  {category.category}
                </Typography>
                <Typography
                  level="bodyLarge"
                  style={{
                    color: "#6c757d",
                    lineHeight: 1.5
                  }}
                >
                  {category.description}
                </Typography>
              </div>

              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))",
                  gap: "20px"
                }}
              >
                {category.items.map((item) => (
                  <ResourceCard
                    key={item.page}
                    page={item.page}
                    title={item.title}
                    description={item.description}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default Catalog
